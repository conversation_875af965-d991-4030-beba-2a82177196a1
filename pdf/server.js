const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { PDFDocument } = require('pdf-lib');
const sharp = require('sharp');
const pdf2pic = require('pdf2pic');
const cors = require('cors');

const app = express();
const port = 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// 配置文件上传
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = './uploads';
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir);
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        cb(null, Date.now() + '-' + file.originalname);
    }
});

const upload = multer({ storage: storage });

// PDF拆分API
app.post('/api/split-pdf', upload.single('pdf'), async (req, res) => {
    try {
        const { splitX, pageNumber = 1 } = req.body;
        const pdfPath = req.file.path;
        
        console.log('开始处理PDF拆分:', { splitX, pageNumber, pdfPath });
        
        // 读取PDF文件
        const pdfBuffer = fs.readFileSync(pdfPath);
        const pdfDoc = await PDFDocument.load(pdfBuffer);
        
        // 获取指定页面
        const pages = pdfDoc.getPages();
        if (pageNumber > pages.length) {
            throw new Error('页面编号超出范围');
        }
        
        const page = pages[pageNumber - 1];
        const { width, height } = page.getSize();
        
        console.log('原始页面尺寸:', { width, height });
        console.log('分割位置:', splitX);
        
        // 将PDF页面转换为高分辨率图像
        const convert = pdf2pic.fromPath(pdfPath, {
            density: 300, // 300 DPI for high quality
            saveFilename: "page",
            savePath: "./temp",
            format: "png",
            width: Math.round(width * 4), // 高分辨率
            height: Math.round(height * 4)
        });
        
        // 转换指定页面
        const result = await convert(pageNumber, { responseType: "buffer" });
        const imageBuffer = result.buffer;
        
        // 使用Sharp处理图像
        const image = sharp(imageBuffer);
        const metadata = await image.metadata();
        
        console.log('图像尺寸:', metadata);
        
        // 计算分割位置（按比例）
        const splitPosition = Math.round((splitX / width) * metadata.width);
        
        // 分割图像
        const leftImage = await image
            .extract({ 
                left: 0, 
                top: 0, 
                width: splitPosition, 
                height: metadata.height 
            })
            .png()
            .toBuffer();
            
        const rightImage = await image
            .extract({ 
                left: splitPosition, 
                top: 0, 
                width: metadata.width - splitPosition, 
                height: metadata.height 
            })
            .png()
            .toBuffer();
        
        // 创建新的PDF文档
        const newPdfDoc = await PDFDocument.create();
        
        // A4尺寸 (595 x 842 points)
        const a4Width = 595;
        const a4Height = 842;
        
        // 嵌入左侧图像
        const leftPngImage = await newPdfDoc.embedPng(leftImage);
        const leftPage = newPdfDoc.addPage([a4Width, a4Height]);
        
        // 计算左侧图像缩放以填充页面
        const leftImageDims = leftPngImage.scale(
            Math.min(a4Width / leftPngImage.width, a4Height / leftPngImage.height)
        );
        
        leftPage.drawImage(leftPngImage, {
            x: (a4Width - leftImageDims.width) / 2,
            y: (a4Height - leftImageDims.height) / 2,
            width: leftImageDims.width,
            height: leftImageDims.height,
        });
        
        // 嵌入右侧图像
        const rightPngImage = await newPdfDoc.embedPng(rightImage);
        const rightPage = newPdfDoc.addPage([a4Width, a4Height]);
        
        // 计算右侧图像缩放以填充页面
        const rightImageDims = rightPngImage.scale(
            Math.min(a4Width / rightPngImage.width, a4Height / rightPngImage.height)
        );
        
        rightPage.drawImage(rightPngImage, {
            x: (a4Width - rightImageDims.width) / 2,
            y: (a4Height - rightImageDims.height) / 2,
            width: rightImageDims.width,
            height: rightImageDims.height,
        });
        
        // 生成PDF
        const pdfBytes = await newPdfDoc.save();
        
        // 清理临时文件
        fs.unlinkSync(pdfPath);
        
        // 清理临时图像文件
        const tempDir = './temp';
        if (fs.existsSync(tempDir)) {
            const files = fs.readdirSync(tempDir);
            files.forEach(file => {
                fs.unlinkSync(path.join(tempDir, file));
            });
        }
        
        // 返回PDF文件
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', 'attachment; filename="split-document.pdf"');
        res.send(Buffer.from(pdfBytes));
        
    } catch (error) {
        console.error('PDF拆分错误:', error);
        res.status(500).json({ error: error.message });
    }
});

// 竖线检测API
app.post('/api/detect-lines', upload.single('pdf'), async (req, res) => {
    try {
        const { pageNumber = 1, sensitivity = 0.5 } = req.body;
        const pdfPath = req.file.path;
        
        // 将PDF转换为图像进行分析
        const convert = pdf2pic.fromPath(pdfPath, {
            density: 150,
            saveFilename: "detect",
            savePath: "./temp",
            format: "png"
        });
        
        const result = await convert(pageNumber, { responseType: "buffer" });
        const imageBuffer = result.buffer;
        
        // 使用Sharp获取图像数据
        const image = sharp(imageBuffer);
        const { data, info } = await image
            .raw()
            .toBuffer({ resolveWithObject: true });
        
        // 实现竖线检测算法
        const detectedLines = detectVerticalLinesServer(data, info.width, info.height, sensitivity);
        
        // 清理临时文件
        fs.unlinkSync(pdfPath);
        
        res.json({ lines: detectedLines });
        
    } catch (error) {
        console.error('竖线检测错误:', error);
        res.status(500).json({ error: error.message });
    }
});

// 服务端竖线检测算法
function detectVerticalLinesServer(imageData, width, height, sensitivity) {
    const detectedLines = [];
    const threshold = Math.floor(255 * (1 - sensitivity));
    const minLineLength = Math.floor(height * 0.3);
    
    // 遍历每一列
    for (let x = 0; x < width; x += 2) {
        let lineSegments = [];
        let currentSegmentStart = -1;
        let currentSegmentLength = 0;
        
        for (let y = 0; y < height; y++) {
            const pixelIndex = (y * width + x) * 3; // RGB
            const r = imageData[pixelIndex];
            const g = imageData[pixelIndex + 1];
            const b = imageData[pixelIndex + 2];
            
            const gray = (r + g + b) / 3;
            
            if (gray < threshold) {
                if (currentSegmentStart === -1) {
                    currentSegmentStart = y;
                    currentSegmentLength = 1;
                } else {
                    currentSegmentLength++;
                }
            } else {
                if (currentSegmentStart !== -1 && currentSegmentLength >= minLineLength) {
                    lineSegments.push({
                        start: currentSegmentStart,
                        end: currentSegmentStart + currentSegmentLength,
                        length: currentSegmentLength
                    });
                }
                currentSegmentStart = -1;
                currentSegmentLength = 0;
            }
        }
        
        if (currentSegmentStart !== -1 && currentSegmentLength >= minLineLength) {
            lineSegments.push({
                start: currentSegmentStart,
                end: currentSegmentStart + currentSegmentLength,
                length: currentSegmentLength
            });
        }
        
        if (lineSegments.length > 0) {
            const totalLength = lineSegments.reduce((sum, segment) => sum + segment.length, 0);
            if (totalLength >= minLineLength) {
                detectedLines.push({
                    x: x,
                    segments: lineSegments,
                    totalLength: totalLength,
                    confidence: Math.min(totalLength / height, 1.0)
                });
            }
        }
    }
    
    return detectedLines.filter(line => line.confidence > 0.4);
}

// 启动服务器
app.listen(port, () => {
    console.log(`PDF处理服务器运行在 http://localhost:${port}`);
});
