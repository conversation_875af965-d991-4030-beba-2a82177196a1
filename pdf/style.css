/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header 样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header .subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

/* 上传区域样式 */
.upload-section {
    margin-bottom: 30px;
}

.upload-area {
    border: 3px dashed #667eea;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    background: #f8f9ff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #764ba2;
    background: #f0f2ff;
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: #28a745;
    background: #f0fff4;
}

.upload-icon {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 20px;
}

.upload-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #333;
}

.upload-content p {
    color: #666;
    margin-bottom: 20px;
}

.upload-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 文件信息样式 */
.file-info {
    background: #e8f5e8;
    border: 1px solid #28a745;
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 15px;
}

.file-icon {
    font-size: 2rem;
    color: #dc3545;
}

.file-text {
    flex: 1;
}

.file-name {
    display: block;
    font-weight: bold;
    color: #333;
}

.file-size {
    display: block;
    color: #666;
    font-size: 0.9rem;
}

.remove-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: #c82333;
    transform: scale(1.1);
}

/* 预览区域样式 */
.preview-section {
    border-top: 2px solid #eee;
    padding-top: 30px;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.preview-header h3 {
    color: #333;
    font-size: 1.5rem;
}

.preview-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.control-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.control-btn:hover {
    background: #764ba2;
    transform: translateY(-1px);
}

.control-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.zoom-level {
    font-weight: bold;
    color: #333;
    min-width: 50px;
    text-align: center;
}

.preview-container {
    text-align: center;
}

#pdfCanvas {
    border: 1px solid #ddd;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    max-width: 100%;
    height: auto;
}

.page-controls {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.page-info {
    font-weight: bold;
    color: #333;
}

/* Footer 样式 */
.footer {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 20px;
}

.footer-section h4 {
    color: #333;
    font-size: 1.2rem;
    margin-bottom: 15px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 5px;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer-section a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #667eea;
}

.footer-section i {
    color: #667eea;
    width: 16px;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #eee;
    color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .upload-area {
        padding: 20px;
    }
    
    .upload-icon {
        font-size: 3rem;
    }
    
    .preview-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .preview-controls {
        justify-content: center;
    }
    
    .page-controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
