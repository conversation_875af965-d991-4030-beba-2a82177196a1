// PDF 预览系统 JavaScript

// 全局变量
let pdfDoc = null;
let currentPage = 1;
let totalPages = 0;
let scale = 1.0;
let currentFile = null;

// 竖线检测相关变量
let lineDetectionEnabled = false;
let detectedLines = [];
let detectionSensitivity = 0.5; // 检测敏感度 (0.1 - 1.0)，默认50%

// DOM 元素
const fileInput = document.getElementById('fileInput');
const uploadArea = document.getElementById('uploadArea');
const fileInfo = document.getElementById('fileInfo');
const fileName = document.getElementById('fileName');
const fileSize = document.getElementById('fileSize');
const removeFile = document.getElementById('removeFile');
const previewSection = document.getElementById('previewSection');
const pdfCanvas = document.getElementById('pdfCanvas');
const ctx = pdfCanvas.getContext('2d');

// 控制按钮
const zoomIn = document.getElementById('zoomIn');
const zoomOut = document.getElementById('zoomOut');
const zoomLevel = document.getElementById('zoomLevel');
const prevPage = document.getElementById('prevPage');
const nextPage = document.getElementById('nextPage');
const currentPageSpan = document.getElementById('currentPage');
const totalPagesSpan = document.getElementById('totalPages');
const downloadBtn = document.getElementById('downloadBtn');
const detectLinesBtn = document.getElementById('detectLinesBtn');

// 设置 PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

// 初始化事件监听器
function initEventListeners() {
    // 文件输入事件
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽事件
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    
    // 移除文件按钮
    removeFile.addEventListener('click', handleRemoveFile);
    
    // 缩放控制
    zoomIn.addEventListener('click', () => changeZoom(0.1));
    zoomOut.addEventListener('click', () => changeZoom(-0.1));
    
    // 页面控制
    prevPage.addEventListener('click', () => changePage(-1));
    nextPage.addEventListener('click', () => changePage(1));
    
    // 下载按钮
    downloadBtn.addEventListener('click', downloadFile);

    // 竖线检测按钮
    detectLinesBtn.addEventListener('click', toggleLineDetection);
}

// 处理文件选择
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file && file.type === 'application/pdf') {
        processFile(file);
    } else {
        alert('请选择有效的 PDF 文件！');
    }
}

// 处理拖拽悬停
function handleDragOver(event) {
    event.preventDefault();
    uploadArea.classList.add('dragover');
}

// 处理拖拽离开
function handleDragLeave(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
}

// 处理文件拖拽
function handleDrop(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type === 'application/pdf') {
            processFile(file);
        } else {
            alert('请选择有效的 PDF 文件！');
        }
    }
}

// 处理文件
function processFile(file) {
    currentFile = file;
    
    // 显示文件信息
    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);
    fileInfo.style.display = 'block';
    
    // 加载 PDF
    loadPDF(file);
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 加载 PDF
async function loadPDF(file) {
    try {
        const arrayBuffer = await file.arrayBuffer();
        pdfDoc = await pdfjsLib.getDocument(arrayBuffer).promise;
        totalPages = pdfDoc.numPages;
        currentPage = 1;
        
        // 更新页面信息
        totalPagesSpan.textContent = totalPages;
        currentPageSpan.textContent = currentPage;
        
        // 显示预览区域
        previewSection.style.display = 'block';
        
        // 渲染第一页
        renderPage(currentPage);
        
        // 更新控制按钮状态
        updateControls();
        
    } catch (error) {
        console.error('加载 PDF 失败:', error);
        alert('加载 PDF 文件失败，请确保文件完整且未损坏。');
    }
}

// 渲染页面
async function renderPage(pageNum) {
    try {
        const page = await pdfDoc.getPage(pageNum);
        const viewport = page.getViewport({ scale: scale });
        
        // 设置 canvas 尺寸
        pdfCanvas.height = viewport.height;
        pdfCanvas.width = viewport.width;
        
        // 渲染页面
        const renderContext = {
            canvasContext: ctx,
            viewport: viewport
        };
        
        await page.render(renderContext).promise;

        // 如果启用了竖线检测，重新绘制检测结果
        if (lineDetectionEnabled && detectedLines.length > 0) {
            drawDetectedLines();
        }

    } catch (error) {
        console.error('渲染页面失败:', error);
        alert('渲染页面失败！');
    }
}

// 改变缩放
function changeZoom(delta) {
    const newScale = scale + delta;
    if (newScale >= 0.5 && newScale <= 3.0) {
        scale = newScale;
        zoomLevel.textContent = Math.round(scale * 100) + '%';
        renderPage(currentPage);

        // 如果启用了竖线检测，重新检测
        if (lineDetectionEnabled) {
            setTimeout(() => {
                detectVerticalLines();
                updateDetectionInfo();
            }, 100); // 等待页面渲染完成
        }
    }
}

// 改变页面
function changePage(delta) {
    const newPage = currentPage + delta;
    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        currentPageSpan.textContent = currentPage;
        renderPage(currentPage);
        updateControls();

        // 如果启用了竖线检测，重新检测当前页面
        if (lineDetectionEnabled) {
            setTimeout(() => {
                detectVerticalLines();
                updateDetectionInfo();
            }, 100); // 等待页面渲染完成
        }
    }
}

// 更新控制按钮状态
function updateControls() {
    prevPage.disabled = currentPage <= 1;
    nextPage.disabled = currentPage >= totalPages;
    zoomOut.disabled = scale <= 0.5;
    zoomIn.disabled = scale >= 3.0;
}

// 移除文件
function handleRemoveFile() {
    currentFile = null;
    pdfDoc = null;
    fileInput.value = '';
    fileInfo.style.display = 'none';
    previewSection.style.display = 'none';
    
    // 清空 canvas
    ctx.clearRect(0, 0, pdfCanvas.width, pdfCanvas.height);
    
    // 重置变量
    currentPage = 1;
    totalPages = 0;
    scale = 1.0;
    zoomLevel.textContent = '100%';
}

// 下载文件
function downloadFile() {
    if (currentFile) {
        const url = URL.createObjectURL(currentFile);
        const a = document.createElement('a');
        a.href = url;
        a.download = currentFile.name;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// 切换竖线检测功能
function toggleLineDetection() {
    lineDetectionEnabled = !lineDetectionEnabled;

    if (lineDetectionEnabled) {
        detectLinesBtn.textContent = '关闭检测';
        detectLinesBtn.classList.add('line-detection-active');
        detectLinesBtn.innerHTML = '<i class="fas fa-eye-slash"></i> 关闭检测';

        // 执行竖线检测
        detectVerticalLines();

        // 显示检测信息
        showDetectionInfo();
    } else {
        detectLinesBtn.textContent = '检测竖线';
        detectLinesBtn.classList.remove('line-detection-active');
        detectLinesBtn.innerHTML = '<i class="fas fa-search"></i> 检测竖线';

        // 清除检测结果
        detectedLines = [];

        // 重新渲染页面（清除红线）
        renderPage(currentPage);

        // 隐藏检测信息
        hideDetectionInfo();
    }
}

// 检测垂直线条
function detectVerticalLines() {
    if (!pdfCanvas || !ctx) return;

    // 获取canvas图像数据
    const imageData = ctx.getImageData(0, 0, pdfCanvas.width, pdfCanvas.height);
    const data = imageData.data;
    const width = pdfCanvas.width;
    const height = pdfCanvas.height;

    detectedLines = [];

    // 垂直线检测算法
    const minLineLength = Math.floor(height * 0.3); // 最小线长度为页面高度的30%
    const threshold = Math.floor(255 * (1 - detectionSensitivity)); // 像素阈值

    // 遍历每一列
    for (let x = 0; x < width; x += 2) { // 每隔2像素检测一次，提高性能
        let lineSegments = [];
        let currentSegmentStart = -1;
        let currentSegmentLength = 0;

        // 遍历当前列的每一行
        for (let y = 0; y < height; y++) {
            const pixelIndex = (y * width + x) * 4;
            const r = data[pixelIndex];
            const g = data[pixelIndex + 1];
            const b = data[pixelIndex + 2];

            // 计算灰度值
            const gray = (r + g + b) / 3;

            // 检测是否为线条（暗色像素）
            if (gray < threshold) {
                if (currentSegmentStart === -1) {
                    currentSegmentStart = y;
                    currentSegmentLength = 1;
                } else {
                    currentSegmentLength++;
                }
            } else {
                // 如果当前段足够长，记录为线段
                if (currentSegmentStart !== -1 && currentSegmentLength >= minLineLength) {
                    lineSegments.push({
                        start: currentSegmentStart,
                        end: currentSegmentStart + currentSegmentLength,
                        length: currentSegmentLength
                    });
                }
                currentSegmentStart = -1;
                currentSegmentLength = 0;
            }
        }

        // 检查最后一个线段
        if (currentSegmentStart !== -1 && currentSegmentLength >= minLineLength) {
            lineSegments.push({
                start: currentSegmentStart,
                end: currentSegmentStart + currentSegmentLength,
                length: currentSegmentLength
            });
        }

        // 如果找到足够长的线段，记录为垂直线
        if (lineSegments.length > 0) {
            const totalLength = lineSegments.reduce((sum, segment) => sum + segment.length, 0);
            if (totalLength >= minLineLength) {
                detectedLines.push({
                    x: x,
                    segments: lineSegments,
                    totalLength: totalLength,
                    confidence: Math.min(totalLength / height, 1.0)
                });
            }
        }
    }

    // 过滤和合并相近的线条
    detectedLines = filterAndMergeLines(detectedLines);

    // 绘制检测到的红线
    drawDetectedLines();

    console.log(`检测到 ${detectedLines.length} 条垂直线`);
}

// 过滤和合并相近的线条
function filterAndMergeLines(lines) {
    if (lines.length === 0) return lines;

    // 按x坐标排序
    lines.sort((a, b) => a.x - b.x);

    const mergedLines = [];
    const mergeDistance = 5; // 5像素内的线条将被合并

    let currentGroup = [lines[0]];

    for (let i = 1; i < lines.length; i++) {
        const currentLine = lines[i];
        const lastInGroup = currentGroup[currentGroup.length - 1];

        if (currentLine.x - lastInGroup.x <= mergeDistance) {
            // 合并到当前组
            currentGroup.push(currentLine);
        } else {
            // 处理当前组并开始新组
            if (currentGroup.length > 0) {
                mergedLines.push(mergeLineGroup(currentGroup));
            }
            currentGroup = [currentLine];
        }
    }

    // 处理最后一组
    if (currentGroup.length > 0) {
        mergedLines.push(mergeLineGroup(currentGroup));
    }

    // 只保留置信度较高的线条
    return mergedLines.filter(line => line.confidence > 0.4);
}

// 合并线条组
function mergeLineGroup(group) {
    if (group.length === 1) return group[0];

    // 计算平均x坐标
    const avgX = Math.round(group.reduce((sum, line) => sum + line.x, 0) / group.length);

    // 合并所有线段
    const allSegments = [];
    group.forEach(line => {
        allSegments.push(...line.segments);
    });

    // 计算总长度和置信度
    const totalLength = group.reduce((sum, line) => sum + line.totalLength, 0);
    const avgConfidence = group.reduce((sum, line) => sum + line.confidence, 0) / group.length;

    return {
        x: avgX,
        segments: allSegments,
        totalLength: totalLength,
        confidence: Math.min(avgConfidence * 1.2, 1.0) // 合并后提高置信度
    };
}

// 绘制检测到的红线
function drawDetectedLines() {
    if (!ctx || detectedLines.length === 0) return;

    // 保存当前绘图状态
    ctx.save();

    // 设置红线样式
    ctx.strokeStyle = '#FF0000';
    ctx.lineWidth = 2;
    ctx.globalAlpha = 0.8;

    // 绘制每条检测到的线
    detectedLines.forEach(line => {
        line.segments.forEach(segment => {
            ctx.beginPath();
            ctx.moveTo(line.x, segment.start);
            ctx.lineTo(line.x, segment.end);
            ctx.stroke();
        });
    });

    // 恢复绘图状态
    ctx.restore();
}

// 显示检测信息
function showDetectionInfo() {
    // 检查是否已存在检测信息面板
    let infoPanel = document.getElementById('detectionInfo');
    if (!infoPanel) {
        // 创建检测信息面板
        infoPanel = document.createElement('div');
        infoPanel.id = 'detectionInfo';
        infoPanel.className = 'detection-info';

        // 插入到预览控制区域后面
        const previewHeader = document.querySelector('.preview-header');
        previewHeader.parentNode.insertBefore(infoPanel, previewHeader.nextSibling);
    }

    // 更新检测信息内容
    updateDetectionInfo();
}

// 隐藏检测信息
function hideDetectionInfo() {
    const infoPanel = document.getElementById('detectionInfo');
    if (infoPanel) {
        infoPanel.remove();
    }
}

// 更新检测信息内容
function updateDetectionInfo() {
    const infoPanel = document.getElementById('detectionInfo');
    if (!infoPanel) return;

    const lineCount = detectedLines.length;
    const avgConfidence = lineCount > 0
        ? (detectedLines.reduce((sum, line) => sum + line.confidence, 0) / lineCount * 100).toFixed(1)
        : 0;

    infoPanel.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
            <i class="fas fa-info-circle"></i>
            <strong>竖线检测结果</strong>
        </div>
        <div class="detection-stats">
            检测到 <strong>${lineCount}</strong> 条垂直线，平均置信度: <strong>${avgConfidence}%</strong>
        </div>
        <div class="detection-controls">
            <div class="sensitivity-control">
                <label for="sensitivitySlider">检测敏感度:</label>
                <input type="range" id="sensitivitySlider" class="sensitivity-slider"
                       min="0.1" max="1.0" step="0.1" value="${detectionSensitivity}">
                <span id="sensitivityValue">${Math.round(detectionSensitivity * 100)}%</span>
            </div>
            <button class="control-btn" id="redetectBtn" style="font-size: 0.8rem; padding: 5px 10px;">
                <i class="fas fa-redo"></i> 重新检测
            </button>
        </div>
    `;

    // 添加敏感度滑块事件监听器
    const sensitivitySlider = document.getElementById('sensitivitySlider');
    const sensitivityValue = document.getElementById('sensitivityValue');
    const redetectBtn = document.getElementById('redetectBtn');

    if (sensitivitySlider) {
        sensitivitySlider.addEventListener('input', function() {
            detectionSensitivity = parseFloat(this.value);
            sensitivityValue.textContent = Math.round(detectionSensitivity * 100) + '%';
        });
    }

    if (redetectBtn) {
        redetectBtn.addEventListener('click', function() {
            // 重新检测
            detectVerticalLines();
            updateDetectionInfo();
        });
    }
}



// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initEventListeners();
    console.log('PDF 预览系统已初始化');
});
