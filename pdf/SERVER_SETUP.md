# 服务端设置指南

本项目支持两种PDF拆分模式：
- **前端模式**：纯浏览器处理，无需服务器
- **服务端模式**：高质量处理，需要Node.js服务器

## 🚀 快速开始

### 前端模式（默认）
直接在浏览器中打开 `index.html` 即可使用，无需额外设置。

### 服务端模式

#### 1. 安装依赖
```bash
# 进入项目目录
cd pdf

# 安装Node.js依赖
npm install
```

#### 2. 启动服务器
```bash
# 开发模式（自动重启）
npm run dev

# 或者生产模式
npm start
```

#### 3. 访问应用
打开浏览器访问：`http://localhost:3000`

## 📦 依赖说明

### 核心依赖
- **express**: Web服务器框架
- **multer**: 文件上传处理
- **pdf-lib**: PDF文档创建和操作
- **sharp**: 高性能图像处理
- **pdf2pic**: PDF转图像
- **cors**: 跨域资源共享

### 系统要求
- Node.js 16.0+
- npm 8.0+
- 支持的操作系统：Windows, macOS, Linux

## 🔧 配置选项

### 服务器配置
在 `server.js` 中可以修改：
```javascript
const port = 3000; // 服务器端口
```

### 图像质量配置
```javascript
density: 300, // DPI设置，影响图像质量
width: Math.round(width * 4), // 图像分辨率倍数
```

## 🎯 两种模式对比

| 特性 | 前端模式 | 服务端模式 |
|------|----------|------------|
| **设置复杂度** | ⭐ 简单 | ⭐⭐⭐ 复杂 |
| **处理质量** | ⭐⭐ 中等 | ⭐⭐⭐⭐⭐ 优秀 |
| **处理速度** | ⭐⭐⭐ 快速 | ⭐⭐ 中等 |
| **文件大小限制** | ⭐⭐ 受浏览器限制 | ⭐⭐⭐⭐ 服务器配置 |
| **网络要求** | ⭐⭐⭐⭐⭐ 无需网络 | ⭐⭐ 需要服务器 |

## 🐛 常见问题

### Q: 安装依赖时出错
A: 确保Node.js版本 >= 16.0，某些依赖需要编译环境

### Q: Sharp安装失败
A: 
```bash
# Windows
npm install --platform=win32 --arch=x64 sharp

# macOS
npm install --platform=darwin --arch=x64 sharp

# Linux
npm install --platform=linux --arch=x64 sharp
```

### Q: pdf2pic转换失败
A: 确保系统安装了以下依赖：
- **Windows**: 无需额外安装
- **macOS**: `brew install poppler`
- **Ubuntu/Debian**: `sudo apt-get install poppler-utils`
- **CentOS/RHEL**: `sudo yum install poppler-utils`

### Q: 服务器启动失败
A: 检查端口是否被占用：
```bash
# 查看端口占用
netstat -an | grep 3000

# 或者修改端口
const port = 3001; // 使用其他端口
```

## 🔄 模式切换

在应用界面中点击右上角的模式切换按钮：
- 🖥️ **前端模式**：浏览器本地处理
- 🖥️ **服务端模式**：服务器高质量处理

## 📁 文件结构

```
pdf/
├── server.js              # Node.js服务器
├── package.json           # 依赖配置
├── index.html             # 主页面
├── script.js              # 前端逻辑
├── style.css              # 样式文件
├── uploads/               # 上传文件目录（自动创建）
├── temp/                  # 临时文件目录（自动创建）
└── SERVER_SETUP.md        # 本文档
```

## 🚀 部署建议

### 开发环境
使用 `npm run dev` 启动开发服务器，支持自动重启。

### 生产环境
1. 使用 PM2 进程管理器：
```bash
npm install -g pm2
pm2 start server.js --name pdf-split-system
```

2. 配置反向代理（Nginx）：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔒 安全注意事项

1. **文件上传限制**：默认限制文件大小，可在multer配置中修改
2. **临时文件清理**：服务器会自动清理临时文件
3. **CORS配置**：生产环境建议限制允许的域名
4. **文件类型验证**：只允许PDF文件上传

## 📞 技术支持

如遇到问题，请检查：
1. Node.js版本是否符合要求
2. 所有依赖是否正确安装
3. 系统是否安装了必要的图像处理库
4. 防火墙是否阻止了端口访问

---

**开发团队**：社交平台架构中心 / 基础架构组一组 / 基础架构五组
