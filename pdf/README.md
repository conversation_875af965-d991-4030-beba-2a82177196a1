# PDF 预览系统

一个功能完整的 PDF 在线预览系统，支持文件上传、在线预览、缩放、翻页等功能。

## 功能特性

### 📁 文件上传
- 支持点击选择文件
- 支持拖拽上传
- 自动验证 PDF 文件格式
- 显示文件信息（文件名、大小）

### 👁️ PDF 预览
- 基于 PDF.js 的高质量渲染
- 支持缩放功能（50% - 300%）
- 支持翻页浏览
- 响应式设计，适配各种屏幕

### 🎨 用户界面
- 现代化的渐变背景设计
- 直观的操作界面
- 平滑的动画效果
- 移动端友好的响应式布局

### 🏢 技术网站 Footer
包含完整的技术网站 Footer 组件，展示：
- **支持**：开放平台、开发者协议、联系我们、在线客服
- **团队**：社交平台架构中心、基础架构组一组、基础架构五组

## 文件结构

```
pdf/
├── index.html              # 主页面
├── style.css              # 样式文件
├── script.js              # JavaScript 功能
├── footer-component.html   # 独立的 Footer 组件
└── README.md              # 项目说明文档
```

## 技术栈

- **前端框架**：原生 HTML5 + CSS3 + JavaScript
- **PDF 渲染**：PDF.js (v3.11.174)
- **图标库**：Font Awesome 6.0.0
- **样式特性**：CSS Grid、Flexbox、CSS 动画

## 使用方法

### 1. 直接打开
在浏览器中打开 `index.html` 文件即可使用。

### 2. 本地服务器（推荐）
为了避免跨域问题，建议使用本地服务器：

```bash
# 使用 Python 3
python -m http.server 8000

# 使用 Node.js (需要安装 http-server)
npx http-server

# 使用 PHP
php -S localhost:8000
```

然后在浏览器中访问 `http://localhost:8000`

### 3. 操作步骤
1. 点击上传区域或拖拽 PDF 文件到页面
2. 系统自动加载并显示 PDF 预览
3. 使用控制按钮进行缩放、翻页操作
4. 可以下载当前文件

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+

## 功能说明

### 上传功能
- 支持的文件格式：PDF (.pdf)
- 文件大小限制：由浏览器内存限制
- 上传方式：本地文件选择（无服务器上传）

### 预览功能
- 缩放范围：50% - 300%
- 页面导航：支持上一页/下一页
- 渲染质量：高清晰度渲染
- 加载状态：显示加载进度

### 响应式设计
- 桌面端：完整功能展示
- 平板端：优化布局
- 移动端：简化操作界面

## 自定义配置

### 修改样式
编辑 `style.css` 文件中的 CSS 变量：

```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --text-color: #333;
    --background-color: #f8f9ff;
}
```

### 修改 PDF.js 配置
在 `script.js` 中修改 PDF.js 设置：

```javascript
// 修改 worker 路径
pdfjsLib.GlobalWorkerOptions.workerSrc = 'your-pdf-worker-path';

// 修改默认缩放
let scale = 1.5; // 默认 150%
```

## 部署说明

### 静态网站部署
本项目为纯前端项目，可以部署到任何静态网站托管服务：

- GitHub Pages
- Netlify
- Vercel
- 阿里云 OSS
- 腾讯云 COS

### 注意事项
1. 确保 PDF.js 库能正常加载
2. 如果使用 CDN，检查网络连接
3. 大文件可能需要较长加载时间

## 开发说明

### 本地开发
1. 克隆或下载项目文件
2. 使用本地服务器运行
3. 修改代码后刷新浏览器查看效果

### 扩展功能
可以考虑添加的功能：
- 文件上传到服务器
- 多文件管理
- 文件分享功能
- 打印功能
- 全屏预览
- 书签功能

## 许可证

MIT License - 可自由使用和修改

## 联系方式

如有问题或建议，请联系：
- 邮箱：<EMAIL>
- 在线客服：工作日 9:00-18:00

---

**技术支持**：社交平台架构中心 / 基础架构组一组 / 基础架构五组
