const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { PDFDocument } = require('pdf-lib');
const sharp = require('sharp');
const cors = require('cors');

const app = express();
const port = 3000;

// 中间件
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.static('.'));

// 配置文件上传
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = './uploads';
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir);
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        cb(null, Date.now() + '-' + file.originalname);
    }
});

const upload = multer({ 
    storage: storage,
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB limit
    }
});

// 简化的PDF拆分API - 接收前端处理的图像数据
app.post('/api/split-pdf-images', express.json({ limit: '50mb' }), async (req, res) => {
    try {
        const { leftImageData, rightImageData, filename } = req.body;
        
        console.log('接收到图像数据进行PDF拆分');
        
        if (!leftImageData || !rightImageData) {
            throw new Error('缺少图像数据');
        }
        
        // 创建新的PDF文档
        const pdfDoc = await PDFDocument.create();
        
        // A4尺寸 (595 x 842 points)
        const a4Width = 595;
        const a4Height = 842;
        
        // 处理左侧图像
        const leftImageBuffer = Buffer.from(leftImageData.split(',')[1], 'base64');
        const leftImage = sharp(leftImageBuffer);
        const leftMetadata = await leftImage.metadata();
        
        // 优化左侧图像 - 提高质量并适配A4
        const leftOptimized = await leftImage
            .resize({
                width: Math.min(a4Width * 2, leftMetadata.width * 2), // 2倍分辨率
                height: Math.min(a4Height * 2, leftMetadata.height * 2),
                fit: 'inside',
                withoutEnlargement: false
            })
            .png({ quality: 100 })
            .toBuffer();
        
        // 处理右侧图像
        const rightImageBuffer = Buffer.from(rightImageData.split(',')[1], 'base64');
        const rightImage = sharp(rightImageBuffer);
        const rightMetadata = await rightImage.metadata();
        
        // 优化右侧图像
        const rightOptimized = await rightImage
            .resize({
                width: Math.min(a4Width * 2, rightMetadata.width * 2),
                height: Math.min(a4Height * 2, rightMetadata.height * 2),
                fit: 'inside',
                withoutEnlargement: false
            })
            .png({ quality: 100 })
            .toBuffer();
        
        // 嵌入左侧图像到PDF
        const leftPngImage = await pdfDoc.embedPng(leftOptimized);
        const leftPage = pdfDoc.addPage([a4Width, a4Height]);
        
        // 计算左侧图像尺寸以填充页面
        const leftScale = Math.min(
            (a4Width - 40) / leftPngImage.width,  // 留20px边距
            (a4Height - 40) / leftPngImage.height
        );
        const leftFinalWidth = leftPngImage.width * leftScale;
        const leftFinalHeight = leftPngImage.height * leftScale;
        
        leftPage.drawImage(leftPngImage, {
            x: (a4Width - leftFinalWidth) / 2,
            y: (a4Height - leftFinalHeight) / 2,
            width: leftFinalWidth,
            height: leftFinalHeight,
        });
        
        // 嵌入右侧图像到PDF
        const rightPngImage = await pdfDoc.embedPng(rightOptimized);
        const rightPage = pdfDoc.addPage([a4Width, a4Height]);
        
        // 计算右侧图像尺寸以填充页面
        const rightScale = Math.min(
            (a4Width - 40) / rightPngImage.width,
            (a4Height - 40) / rightPngImage.height
        );
        const rightFinalWidth = rightPngImage.width * rightScale;
        const rightFinalHeight = rightPngImage.height * rightScale;
        
        rightPage.drawImage(rightPngImage, {
            x: (a4Width - rightFinalWidth) / 2,
            y: (a4Height - rightFinalHeight) / 2,
            width: rightFinalWidth,
            height: rightFinalHeight,
        });
        
        // 生成PDF
        const pdfBytes = await pdfDoc.save();
        
        // 返回PDF文件
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="${filename || 'split-document.pdf'}"`);
        res.send(Buffer.from(pdfBytes));
        
        console.log('PDF拆分完成');
        
    } catch (error) {
        console.error('PDF拆分错误:', error);
        res.status(500).json({ error: error.message });
    }
});

// 健康检查API
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        message: 'PDF处理服务器运行正常',
        timestamp: new Date().toISOString()
    });
});

// 获取服务器信息
app.get('/api/info', (req, res) => {
    res.json({
        name: 'PDF Split Server',
        version: '1.0.0',
        features: ['pdf-split', 'image-optimization'],
        maxFileSize: '50MB'
    });
});

// 启动服务器
app.listen(port, () => {
    console.log(`PDF处理服务器运行在 http://localhost:${port}`);
    console.log('支持的功能:');
    console.log('- POST /api/split-pdf-images - PDF图像拆分');
    console.log('- GET /api/health - 健康检查');
    console.log('- GET /api/info - 服务器信息');
});
