# PDF 预览系统

一个功能完整的 PDF 在线预览系统，支持文件上传、在线预览、缩放、翻页等功能。

## 功能特性

### 📁 文件上传
- 支持点击选择文件
- 支持拖拽上传
- 自动验证 PDF 文件格式
- 显示文件信息（文件名、大小）

### 👁️ PDF 预览
- 基于 PDF.js 的高质量渲染
- 支持缩放功能（50% - 300%）
- 支持翻页浏览
- 响应式设计，适配各种屏幕

### 🔍 智能竖线检测
- 自动识别PDF文档中的垂直分割线
- 用红色直线标记检测到的竖线
- 可调节检测敏感度（10% - 100%，默认50%）
- 实时显示检测统计信息
- 支持重新检测功能

### ✂️ 智能PDF拆分
- 基于检测到的竖线自动拆分PDF页面
- 将单页拆分为两个A4尺寸的页面
- 智能选择最佳分割线（置信度最高且位置居中）
- 生成新的PDF文档并提供下载
- 实时显示拆分进度和结果统计
- 支持前端和服务端两种处理模式

### 🎨 用户界面
- 现代化的渐变背景设计
- 直观的操作界面
- 平滑的动画效果
- 移动端友好的响应式布局

### 🏢 技术网站 Footer
包含完整的技术网站 Footer 组件，展示：
- **支持**：开放平台、开发者协议、联系我们、在线客服
- **团队**：社交平台架构中心、基础架构组一组、基础架构五组

## 文件结构

```
pdf/
├── index.html              # 主页面
├── style.css              # 样式文件
├── script.js              # JavaScript 功能
├── footer-component.html   # 独立的 Footer 组件
├── server.js              # Node.js 服务器（可选）
├── package.json           # 服务端依赖配置
├── SERVER_SETUP.md        # 服务端设置指南
├── demo.html              # 功能演示页面
└── README.md              # 项目说明文档
```

## 技术栈

- **前端框架**：原生 HTML5 + CSS3 + JavaScript
- **PDF 渲染**：PDF.js (v3.11.174)
- **PDF 创建**：PDF-lib (v1.17.1)
- **图标库**：Font Awesome 6.0.0
- **样式特性**：CSS Grid、Flexbox、CSS 动画

## 使用方法

### 1. 前端模式（默认）
直接在浏览器中打开 `index.html` 文件即可使用。

### 2. 服务端模式（高质量处理）
如需更高质量的PDF拆分效果，可启用服务端模式：

```bash
# 安装依赖
npm install

# 启动服务器
npm start
```

然后在浏览器中访问 `http://localhost:3000`

详细的服务端设置请参考 [SERVER_SETUP.md](SERVER_SETUP.md)

### 3. 操作步骤
1. 点击上传区域或拖拽 PDF 文件到页面
2. 系统自动加载并显示 PDF 预览
3. 使用控制按钮进行缩放、翻页操作
4. 点击"检测竖线"按钮启用智能竖线识别
5. 调整检测敏感度以获得最佳检测效果
6. 点击"拆分PDF"按钮根据竖线拆分页面
7. 下载拆分后的新PDF文档或原始文件

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+

## 功能说明

### 上传功能
- 支持的文件格式：PDF (.pdf)
- 文件大小限制：由浏览器内存限制
- 上传方式：本地文件选择（无服务器上传）

### 预览功能
- 缩放范围：50% - 300%
- 页面导航：支持上一页/下一页
- 渲染质量：高清晰度渲染
- 加载状态：显示加载进度

### 响应式设计
- 桌面端：完整功能展示
- 平板端：优化布局
- 移动端：简化操作界面

## 自定义配置

### 修改样式
编辑 `style.css` 文件中的 CSS 变量：

```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --text-color: #333;
    --background-color: #f8f9ff;
}
```

### 修改 PDF.js 配置
在 `script.js` 中修改 PDF.js 设置：

```javascript
// 修改 worker 路径
pdfjsLib.GlobalWorkerOptions.workerSrc = 'your-pdf-worker-path';

// 修改默认缩放
let scale = 1.5; // 默认 150%
```

## 部署说明

### 静态网站部署
本项目为纯前端项目，可以部署到任何静态网站托管服务：

- GitHub Pages
- Netlify
- Vercel
- 阿里云 OSS
- 腾讯云 COS

### 注意事项
1. 确保 PDF.js 库能正常加载
2. 如果使用 CDN，检查网络连接
3. 大文件可能需要较长加载时间

## 开发说明

### 本地开发
1. 克隆或下载项目文件
2. 使用本地服务器运行
3. 修改代码后刷新浏览器查看效果

### 竖线检测功能详解

#### 检测原理
- 使用像素级分析算法扫描PDF页面
- 识别连续的垂直暗色像素序列
- 过滤短线段，只保留长度超过页面30%的线条
- 合并相近的线条以减少误检

#### 检测参数
- **敏感度**：控制检测的严格程度（默认50%）
  - 低敏感度（10%-40%）：只检测明显的粗线
  - 中敏感度（50%-70%）：平衡检测效果（推荐）
  - 高敏感度（80%-100%）：检测细微线条
- **最小线长**：线条长度必须超过页面高度的30%
- **合并距离**：5像素内的线条会被合并

#### 使用技巧
1. 默认50%敏感度适合大多数文档
2. 对于扫描文档，建议使用中等敏感度（40%-60%）
3. 对于清晰的电子文档，可以使用较高敏感度（70%-90%）
4. 如果检测结果过多，降低敏感度
5. 如果遗漏明显线条，提高敏感度

### PDF拆分功能详解

#### 拆分原理
- 基于检测到的竖线位置进行页面分割
- 智能选择最佳分割线（置信度最高且位置居中）
- 将原页面按分割线分为左右两部分
- 每部分缩放适配A4尺寸（595 x 842 points）
- 生成包含两个页面的新PDF文档

#### 拆分流程
1. **检测竖线**：首先需要启用竖线检测功能
2. **选择分割线**：系统自动选择最佳的竖线作为分割线
3. **页面渲染**：分别渲染左右两部分内容
4. **尺寸适配**：将内容缩放适配A4页面尺寸
5. **PDF生成**：使用PDF-lib创建新的PDF文档
6. **文件下载**：提供拆分后PDF的下载功能

#### 分割线选择算法
- **置信度权重（70%）**：优先选择检测置信度高的线条
- **位置权重（30%）**：优先选择接近页面中心的线条
- **综合评分**：置信度 × 0.7 + 位置得分 × 0.3
- **最佳选择**：选择综合评分最高的线条作为分割线

#### 使用场景
- **双栏文档**：将双栏布局的文档拆分为单栏页面
- **扫描书籍**：将扫描的书籍页面拆分为单页
- **表格文档**：按列分割大型表格
- **技术图纸**：分割包含多个图表的页面

#### 处理模式对比

| 特性 | 前端模式 | 服务端模式 |
|------|----------|------------|
| **设置复杂度** | ⭐ 简单 | ⭐⭐⭐ 复杂 |
| **处理质量** | ⭐⭐ 中等 | ⭐⭐⭐⭐⭐ 优秀 |
| **处理速度** | ⭐⭐⭐ 快速 | ⭐⭐ 中等 |
| **文件大小限制** | ⭐⭐ 受浏览器限制 | ⭐⭐⭐⭐ 服务器配置 |
| **网络要求** | ⭐⭐⭐⭐⭐ 无需网络 | ⭐⭐ 需要服务器 |
| **图像质量** | 标准Canvas渲染 | 300DPI高清渲染 |
| **内存使用** | 浏览器内存限制 | 服务器内存管理 |

#### 注意事项
- 拆分功能需要先检测到竖线才能使用
- 建议在检测到清晰分割线时使用此功能
- 拆分后的页面会自动适配A4尺寸
- 原始PDF文件不会被修改
- 服务端模式提供更高质量的拆分效果

### 扩展功能
可以考虑添加的功能：
- 文件上传到服务器
- 多文件管理
- 文件分享功能
- 打印功能
- 全屏预览
- 书签功能
- 水平线检测
- 表格边框识别
- 批量拆分多页PDF
- 自定义拆分位置

## 许可证

MIT License - 可自由使用和修改

## 联系方式

如有问题或建议，请联系：
- 邮箱：<EMAIL>
- 在线客服：工作日 9:00-18:00

---

**技术支持**：社交平台架构中心 / 基础架构组一组 / 基础架构五组
